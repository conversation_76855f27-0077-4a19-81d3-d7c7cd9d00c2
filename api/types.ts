export interface reportDataInfo {
  researcherProfile: ResearcherProfile
}

export interface ResearcherProfile {
  researcherInfo: ResearcherInfo
  dataBlocks: DataBlocks
  configInfo: ConfigInfo
}

export interface ConfigInfo {
  comment: string
}

export interface DataBlocks {
  publicationStats: PublicationStats
  publicationInsight: PublicationInsight
  roleModel: RoleModel
  closestCollaborator: ClosestCollaborator
  estimatedSalary: EstimatedSalary
  researcherCharacter: ResearcherCharacter
  representativePaper: RepresentativePaper
  criticalReview: CriticalReview
}

export interface CriticalReview {
  blockTitle: string
  evaluation: string
}

export interface RepresentativePaper {
  blockTitle: string
  title: string
  year: number
  venue: string
  fullVenue: string
  citations: number
  authorPosition: null
}

export interface ResearcherCharacter {
  blockTitle: string
  depthVsBreadth: number
  theoryVsPractice: number
  soloVsTeamwork: number
  justification: null
}

export interface EstimatedSalary {
  blockTitle: string
  earningsPerYearUSD: number
  levelEquivalency: LevelEquivalency
  reasoning: string
}

export interface LevelEquivalency {
  us: string
  cn: string
}

export interface ClosestCollaborator {
  blockTitle: string
  fullName: string
  affiliation: string
  researchInterests: any[]
  scholarId: string
  coauthoredPapers: number
  bestCoauthoredPaper: BestCoauthoredPaper
  connectionAnalysis: null
}

export interface BestCoauthoredPaper {
  title: string
  year: number
  venue: string
  fullVenue: string
  citations: number
}

export interface RoleModel {
  blockTitle: string
  found: boolean
  name: null
  institution: null
  position: null
  photoUrl: null
  achievement: null
  similarityReason: null
}

export interface PublicationInsight {
  blockTitle: string
  totalPapers: number
  topTierPapers: number
  firstAuthorPapers: number
  firstAuthorCitations: number
  totalCoauthors: number
  lastAuthorPapers: number
  conferenceDistribution: ConferenceDistribution
}

export interface ConferenceDistribution {
  CVPR: number
  ICCV: number
  'ACM MM': number
  ICASSP: number
  NeurIPS: number
  Others: number
}

export interface PublicationStats {
  blockTitle: string
  totalPapers: number
  totalCitations: number
  hIndex: number
  yearlyCitations: YearlyCitations
  yearlyPapers: YearlyPapers
}

export interface YearlyPapers {
  [year: string]: number
}

export interface ResearcherInfo {
  avatar: string
  description: string
  name: string
  abbreviatedName: string
  affiliation: string
  email: null
  researchFields: string[]
  totalCitations: number
  citations5y: number
  hIndex: number
  hIndex5y: number
  yearlyCitations: YearlyCitations
  twitter?: string
  github?: string
  scholar?: string
  scholarId?: string
}

export interface YearlyCitations {
  [year: string]: number
}

export interface TalentRes {
  moves: any
  success: boolean
  count: number
  talents: Talent[]
}

export interface Talent {
  citation: string
  famous_work: string
  google_scholar: string
  honor: string
  image: string
  institution: string
  linkedin: string
  name: string
  personal_page: string
  position: string
  twitter: string
}
// --- API Response Types (from previous step) ---

export interface NewRecommendedPaper {
  id: string;
  title: string;
  authors: string[];
  author_ids: string[];
  keywords: string[];
  primary_area: string[];
  position: string[];
  aff: string[];
  year: string;
  source: string;
  status: string; // 添加status字段
  tags: string[]; // 新增tags字段
  profiles: AuthorProfile[]; // 新增profiles数组
  author_info: {
    author: string;
    author_id: string;
    score: number;
    recommend_reason: string | string[]; // 支持字符串或字符串数组格式
    gender: string;
    position: string;
    affiliation: string;
    scholar_id: string;
    avatar_url: string;
  };
}

export interface AuthorProfile {
  author: string;
  author_id: string;
  scholar_id: string;
  avatar_url: string;
  position: string;
  affiliation: string;
  gender: string;
}

export interface RecommendDataItem {
  data_type: 'paper' | 'github';  // 数据类型
  data: NewRecommendedPaper | GitHubData;  // 实际数据，根据data_type决定类型
}

export interface GitHubData {
  bio: string;
  login: string;
  repositories: string; // JSON字符串格式的仓库列表
  name: string;
  avatar_url: string;
  tags: string[];
  repository: {
    name: string;
    about: string;
    tags: string[];
    stars: number;
  };
  position: string;
  score: number;
  recommend_reason: string[];
}

export interface NewRecommendPapersResponse {
  data: RecommendDataItem[];  // 现在是包装过的数据数组
}

// Legacy types - keeping for backward compatibility
export interface RecommendedPaperDetail {
  score: number;
  title: string;
  paper_id: string;
  authors: string[];
  status: string;
  keywords: string;
  position: string;
  aff: string;
  primary_area: string;
  first_author: string;
  author_ids: string[];
  year: string;
  journal: string;
}

export interface RecommendedAuthor {
  name: string;
  gender: string;
  author_id: string;
  scholar_id: string | null;
  position: string; // Author's primary position
  avatar_url: string;
  affiliation: string; // Author's primary affiliation
  paper: RecommendedPaperDetail;
  recommend_reason: string;
  tags: string; // Comma-separated string
  score: number; // Overall recommendation score for the author
}

export interface QueryExpansion {
  expanded_query: string;
  author: string;
  institution: string;
  research_field: string;
  affiliation: string;
}

export interface RecommendPapersData {
  authors: RecommendedAuthor[];
  query_expand: QueryExpansion;
}

export interface RecommendPapersResponse {
  data: RecommendPapersData;
  cost: number;
  status: string;
}

// --- Display Model for your Card Component ---
export interface DisplayCandidate {
  id: string; // from author_id
  name: string;
  // 'position' in API is the author's primary position, 'affiliation' is their institution
  positionTitle: string; // Mapped from API's 'position'
  institution: string;   // Mapped from API's 'affiliation'
  avatarUrl?: string;   // Mapped from API's 'avatar_url'
  skills: string[];      // Mapped and split from API's 'tags'
  summary?: string;      // Mapped from API's profile.summary
  profile?: any;         // Original profile data for accessing github, scholar, etc.
  data?: any;            // Original data object for accessing scholar, etc.
  featuredWork: {
    title: string;       // Mapped from API's paper.title
    venue: string;       // Mapped from API's paper.journal
    type: string;        // Mapped from API's paper.status
    year?: string;       // Mapped from API's paper.year
  };
  // 'recommend_reason' from API is a single string.
  // Your card expects `recommendations: string[]`. We'll split it or use it as a single item.
  recommendations: string[];
  matchScore: number;    // Mapped from API's 'score' (overall author score)
  author_ids?: string;   // OpenReview ID for network API
  // These are not directly in the API response, you might need to adjust or mock
  paperCount?: number;   // Not directly available in the provided API response for this specific structure
  citations?: number;    // Not directly available
  isFirstAuthor?: boolean; // Can be derived if needed, but not directly from top-level
}

// Graph API Types - 统一的返回格式
export interface GraphUser {
  name: string;
  analyze_id: string;
  data_id?: string; // 新增data_id字段，用于paper类型
  avatar_url: string;
  position: string;
  company?: string; // 新增company字段
  data_type: "paper" | "github";
}

export interface GraphResponse {
  data: GraphUser[];
}

export interface GraphParams {
  user: string; // OpenReview用户ID（type=paper时）或GitHub login（type=github时）
  type: 'paper' | 'github'; // 数据类型
}

// Network API Types - 简化的Network API参数和响应类型
export interface NetworkParams {
  network_id: string; // 简化为单一的network_id参数
}

export interface NetworkResponse {
  data: GraphUser[]; // 复用GraphUser类型，因为返回格式相同
}

// Email API Types
export interface EmailParams {
  profile_id: string;
}

export interface EmailResponse {
  data?: string;  // 邮箱地址字符串，例如 "<EMAIL>"
  message?: string;
  error?: string;
}

export interface UserCard {
  age: number
  avatar_url: string
  created_at: any
  education: Array<any>
  from_company: string
  id: number
  major_achievement: Array<any>
  person_name: string
  post_image_url: string
  query: string
  salary: string
  talent_description: string
  to_company: string
  tweet_url: string
  work_experience: Array<any>
  to_company_logo_url: string
  from_company_logo_url: string
}

// LinkedIn Talents API Types
export interface LinkedInTalent {
  name: string
  position: string
  company: string
  image: string
  linkedin_url: string
  bio?: string
  skills?: string[]
  location?: string
  experience_years?: number
  industry?: string
}

export interface LinkedInTalentsResponse {
  success: boolean
  count: number
  total_available: number
  talents: LinkedInTalent[]
  metadata?: {
    query?: string
    filters_applied?: Record<string, any>
  }
}